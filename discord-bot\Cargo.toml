[package]
name = "voidbot-discord"
version.workspace = true
edition.workspace = true

[[bin]]
name = "voidbot-bot"
path = "src/main.rs"

[dependencies]
# Workspace deps
voidbot-shared = { path = "../shared" }
tokio.workspace = true
serde.workspace = true
serde_json.workspace = true
anyhow.workspace = true
tracing.workspace = true
tracing-subscriber.workspace = true
sqlx.workspace = true
chrono.workspace = true
uuid.workspace = true
reqwest.workspace = true

# Discord - VERSION ÉPINGLÉE
serenity = { version = "=0.12.2", features = ["client", "gateway", "model", "framework", "standard_framework", "rustls_backend", "cache", "collector"] }

# Utils - VERSIONS ÉPINGLÉES
dotenv = "=0.15.0"
regex = "=1.10.6"
once_cell = "=1.21.0"
fastrand = "=2.0.2"
rand = "=0.8.5"

# Image generation - VERSIONS ÉPINGLÉES
image = "=0.24.9"
imageproc = "=0.23.0"
base64 = "=0.21.7"

# Serveur HTTP pour Railway health check
warp = "=0.3.7"
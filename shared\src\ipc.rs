use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

/// Messages IPC entre l'app desktop et le bot Discord
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum IpcMessage {
    // Bot → Desktop : Événements à logger en base
    LogEvent {
        event_type: String,
        severity: String,
        message: String,
        context: Option<String>,
    },
    
    // Bot → Desktop : Incrémenter usage feature
    IncrementUsage {
        feature: String,
        action: String,
        metadata: Option<String>,
    },
    
    // Bot → Desktop : Log commande exécutée
    LogCommand {
        command_name: String,
        parameters: Option<String>,
        user_id: Option<String>,
        guild_id: Option<String>,
        channel_id: Option<String>,
        success: bool,
        error_message: Option<String>,
        execution_time_ms: Option<u64>,
    },
    
    // Bot → Desktop : Giveaway détecté
    GiveawayDetected {
        guild_id: String,
        channel_id: String,
        message_id: String,
        title: String,
        description: Option<String>,
        end_time: Option<DateTime<Utc>>,
        requirements: Vec<String>,
        host: String,
    },
    
    // Bot → Desktop : Code Nitro détecté
    NitroCodeDetected {
        code: String,
        channel_id: String,
        guild_id: Option<String>,
        claimed: bool,
        claim_time_ms: Option<u64>,
        value: Option<String>,
    },
    
    // Bot → Desktop : Notification événement
    NotificationEvent {
        notification_type: String,
        title: String,
        message: String,
        guild_id: Option<String>,
        channel_id: Option<String>,
        user_id: Option<String>,
        metadata: Option<String>,
    },
    
    // Desktop → Bot : Configuration changée
    ConfigUpdated {
        category: String,
        key: String,
        value: String,
    },
    
    // Desktop → Bot : Commande de contrôle
    BotControl {
        action: BotControlAction,
    },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BotControlAction {
    // Contrôle du bot
    Start,
    Stop,
    Restart,
    
    // Contrôle features
    EnableFeature(String),
    DisableFeature(String),
    
    // Configuration
    UpdateStealthMode(String),
    UpdateRateLimit(String),
    
    // Nettoyage
    ClearCache,
    ResetStats,
}

/// Client IPC pour communication entre composants
#[async_trait::async_trait]
pub trait IpcClient: Send + Sync {
    async fn send_message(&self, message: IpcMessage) -> Result<(), IpcError>;
    async fn receive_messages(&mut self) -> Result<Vec<IpcMessage>, IpcError>;
}

/// Erreurs IPC
#[derive(Debug, thiserror::Error)]
pub enum IpcError {
    #[error("Erreur de connexion IPC: {0}")]
    ConnectionError(String),
    
    #[error("Erreur de sérialisation: {0}")]
    SerializationError(String),
    
    #[error("Message invalide: {0}")]
    InvalidMessage(String),
    
    #[error("Timeout IPC: {0}ms")]
    Timeout(u64),
    
    #[error("Canal fermé")]
    ChannelClosed,
}

/// Implementation en mémoire pour développement/tests
pub struct InMemoryIpcClient {
    sender: tokio::sync::mpsc::UnboundedSender<IpcMessage>,
    receiver: tokio::sync::mpsc::UnboundedReceiver<IpcMessage>,
}

impl InMemoryIpcClient {
    pub fn new() -> (Self, tokio::sync::mpsc::UnboundedReceiver<IpcMessage>) {
        let (tx, rx) = tokio::sync::mpsc::unbounded_channel();
        let (_internal_tx, internal_rx) = tokio::sync::mpsc::unbounded_channel();
        
        let client = Self {
            sender: tx,
            receiver: internal_rx,
        };
        
        (client, rx)
    }
}

#[async_trait::async_trait]
impl IpcClient for InMemoryIpcClient {
    async fn send_message(&self, message: IpcMessage) -> Result<(), IpcError> {
        self.sender.send(message)
            .map_err(|_| IpcError::ChannelClosed)?;
        Ok(())
    }
    
    async fn receive_messages(&mut self) -> Result<Vec<IpcMessage>, IpcError> {
        let mut messages = Vec::new();
        
        // Collecte tous les messages disponibles sans bloquer
        while let Ok(message) = self.receiver.try_recv() {
            messages.push(message);
        }
        
        Ok(messages)
    }
}

/// Helper pour logging via IPC
pub async fn log_event_via_ipc<T: IpcClient>(
    ipc: &T,
    event_type: &str,
    severity: &str,
    message: &str,
    context: Option<&str>,
) -> Result<(), IpcError> {
    let message = IpcMessage::LogEvent {
        event_type: event_type.to_string(),
        severity: severity.to_string(),
        message: message.to_string(),
        context: context.map(|s| s.to_string()),
    };
    
    ipc.send_message(message).await
}

/// Helper pour incrémenter usage via IPC
pub async fn increment_usage_via_ipc<T: IpcClient>(
    ipc: &T,
    feature: &str,
    action: &str,
    metadata: Option<&str>,
) -> Result<(), IpcError> {
    let message = IpcMessage::IncrementUsage {
        feature: feature.to_string(),
        action: action.to_string(),
        metadata: metadata.map(|s| s.to_string()),
    };
    
    ipc.send_message(message).await
}
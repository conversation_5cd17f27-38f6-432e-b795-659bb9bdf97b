use anyhow::Result;
use serenity::{
    async_trait,
    model::{
        application::{CommandInteraction, Interaction},
        gateway::Ready,
        prelude::*,
    },
    prelude::*,
};
use std::sync::Arc;
use tokio::sync::Mutex;
use tracing::{error, info, warn};
use voidbot_shared::{Config, StealthMode, RateLimiter, RateLimitConfig};

// Imports pour le serveur HTTP de sanité Railway
use std::net::SocketAddr;
use tokio::net::TcpListener;
use warp::Filter;

mod commands;
mod handlers;
mod stealth;
mod notifications;
mod auto_commands;
mod giveaway_joiner;
mod nitro_sniper;
mod rate_limited_http;
mod activity_tracker;
mod event_emitter;
mod error_handling;

#[cfg(test)]
mod tests;

use commands::{register_commands, troll::TrollManager};
use handlers::*;
use stealth::StealthManager;
use notifications::DiscordNotificationHandler;
use auto_commands::AutoCommandsManager;
use giveaway_joiner::GiveawayJoiner;
use nitro_sniper::NitroSniper;
use activity_tracker::ActivityTracker;
use event_emitter::{EventEmitter, create_test_event_emitter, create_production_event_emitter};
use error_handling::{VoidBotError, ErrorHandler};

pub struct BotData {
    pub stealth_manager: Arc<Mutex<StealthManager>>,
    pub config: Config,
    pub notification_handler: Arc<DiscordNotificationHandler>,
    pub troll_manager: Arc<TrollManager>,
    pub auto_commands_manager: Arc<AutoCommandsManager>,
    pub giveaway_joiner: Arc<GiveawayJoiner>,
    pub nitro_sniper: Arc<NitroSniper>,
    pub rate_limiter: Arc<RateLimiter>,
    pub activity_tracker: Arc<ActivityTracker>,
    pub event_emitter: Arc<EventEmitter>,
}

impl TypeMapKey for BotData {
    type Value = Arc<BotData>;
}

struct Handler;

#[async_trait]
impl EventHandler for Handler {
    async fn ready(&self, ctx: Context, ready: Ready) {
        info!("Selfbot connecté en tant que : {}", ready.user.name);
        
        // Enregistrer les commandes slash
        if let Err(e) = register_commands(&ctx).await {
            error!("Échec de l'enregistrement des commandes : {}", e);
        } else {
            info!("Commandes slash enregistrées avec succès");
        }
    }

    async fn interaction_create(&self, ctx: Context, interaction: Interaction) {
        if let Interaction::Command(command) = interaction {
            if let Err(e) = handle_slash_command(&ctx, &command).await {
                error!("Erreur lors du traitement de la commande slash : {}", e);
            }
        }
    }

    async fn message(&self, ctx: Context, message: Message) {
        if let Some(bot_data) = ctx.data.read().await.get::<BotData>() {
            // Traiter les messages pour les notifications
            if let Err(e) = bot_data.notification_handler.handle_message_create(&ctx, &message).await {
                error!("Erreur lors du traitement du message pour notifications : {}", e);
            }
            
            // Traiter les messages pour les auto-commands
            if let Err(e) = bot_data.auto_commands_manager.handle_message(&ctx, &message).await {
                error!("Erreur lors du traitement du message pour auto-commands : {}", e);
            }
            
            // Analyser le message pour détecter des giveaways
            if let Some(giveaway) = bot_data.giveaway_joiner.analyze_message(&ctx, &message).await {
                if let Some(event) = bot_data.giveaway_joiner.handle_detected_giveaway(&ctx, giveaway).await {
                    info!("Giveaway event: {:?}", event);

                    // Envoyer l'événement au frontend via l'event emitter
                    let giveaway_info = serde_json::json!({
                        "message_id": message.id.to_string(),
                        "channel_id": message.channel_id.to_string(),
                        "guild_id": message.guild_id.map(|g| g.to_string()),
                        "author": message.author.name,
                        "content": message.content,
                        "event_details": event
                    });

                    if let Err(e) = bot_data.event_emitter.emit_giveaway_event("detected", giveaway_info).await {
                        warn!("Erreur lors de l'émission d'événement giveaway: {}", e);
                    }
                }
            }

            // Analyser le message pour détecter des codes Nitro (ULTRA-PRIORITAIRE)
            if let Some(codes) = bot_data.nitro_sniper.analyze_message_fast(&ctx, &message).await {
                let events = bot_data.nitro_sniper.handle_detected_codes(&ctx, codes).await;
                for event in events {
                    info!("Nitro sniper event: {:?}", event);

                    // Envoyer l'événement au frontend via l'event emitter
                    let nitro_info = serde_json::json!({
                        "message_id": message.id.to_string(),
                        "channel_id": message.channel_id.to_string(),
                        "guild_id": message.guild_id.map(|g| g.to_string()),
                        "author": message.author.name,
                        "event_details": event
                    });

                    if let Err(e) = bot_data.event_emitter.emit_nitro_event("detected", nitro_info).await {
                        warn!("Erreur lors de l'émission d'événement Nitro: {}", e);
                    }
                }
            }
        }
    }

    async fn message_delete(&self, ctx: Context, channel_id: ChannelId, deleted_message_id: MessageId, guild_id: Option<GuildId>) {
        // Traiter les suppressions de messages (ghostping)
        if let Some(bot_data) = ctx.data.read().await.get::<BotData>() {
            if let Err(e) = bot_data.notification_handler.handle_message_delete(&ctx, channel_id, deleted_message_id, guild_id).await {
                error!("Erreur lors du traitement de la suppression de message : {}", e);
            }
        }
    }

    async fn guild_ban_addition(&self, ctx: Context, guild_id: GuildId, banned_user: User) {
        // Traiter les bannissements
        if let Some(bot_data) = ctx.data.read().await.get::<BotData>() {
            if let Err(e) = bot_data.notification_handler.handle_guild_ban_addition(&ctx, guild_id, banned_user).await {
                error!("Erreur lors du traitement du bannissement : {}", e);
            }
        }
    }

    // TODO: Fix guild_member_update signature for Serenity 0.12.2
    // async fn guild_member_update(&self, ctx: Context, old_if_available: Option<Member>, new: Member) {
    //     // Traiter les changements de membre (rôles, etc.)
    //     if let Some(bot_data) = ctx.data.read().await.get::<BotData>() {
    //         if let Err(e) = bot_data.notification_handler.handle_guild_member_update(&ctx, old_if_available, new).await {
    //             error!("Erreur lors du traitement de la mise à jour de membre : {}", e);
    //         }
    //     }
    // }

    // TODO: Fix presence_update signature for Serenity 0.12.2
    // async fn presence_update(&self, ctx: Context, new_data: PresenceUpdateEvent) {
    //     // Traiter les changements de présence pour l'Activity Viewer
    //     if let Some(bot_data) = ctx.data.read().await.get::<BotData>() {
    //         bot_data.activity_tracker.handle_presence_update(&ctx, &new_data.presence).await;
    //     }
    // }

    async fn voice_state_update(&self, ctx: Context, old: Option<VoiceState>, new: VoiceState) {
        // Traiter les changements d'état vocal pour l'Activity Viewer
        if let Some(bot_data) = ctx.data.read().await.get::<BotData>() {
            bot_data.activity_tracker.handle_voice_state_update(&ctx, &new).await;
        }
    }

    async fn typing_start(&self, ctx: Context, event: TypingStartEvent) {
        // Traiter les événements de frappe pour l'Activity Viewer
        if let Some(bot_data) = ctx.data.read().await.get::<BotData>() {
            bot_data.activity_tracker.handle_typing_start(&ctx, &event).await;
        }
    }

    // TODO: Fix user_update signature for Serenity 0.12.2
    // async fn user_update(&self, ctx: Context, old_data: CurrentUser, new: CurrentUser) {
    //     // Traiter les mises à jour d'utilisateur pour l'Activity Viewer
    //     if let Some(bot_data) = ctx.data.read().await.get::<BotData>() {
    //         bot_data.activity_tracker.handle_user_update(&ctx, &Some(old_data), &new).await;
    //     }
    // }
}

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize tracing
    tracing_subscriber::fmt::init();

    // Load config
    let config = Config::default(); // TODO: Load from file

    // Vérifier si on est en mode test
    let test_mode = std::env::var("VOIDBOT_TEST_MODE").unwrap_or_default() == "true";
    
    // Charger le token depuis les variables d'environnement (Railway) ou stockage local
    let token = if let Ok(env_token) = std::env::var("DISCORD_TOKEN") {
        if test_mode && env_token.contains("test_token_mock") {
            info!("🧪 MODE TEST DÉTECTÉ - Bot fonctionnera en mode simulation");
            info!("📝 Interface sera accessible mais Discord non connecté");
            
            // Démarrer un serveur HTTP simple pour simuler l'activité
            tokio::spawn(async {
                let routes = warp::path("status")
                    .map(|| "VoidBot Test Mode - Bot Running");
                
                info!("🚀 Serveur de test démarré sur http://localhost:3001");
                warp::serve(routes)
                    .run(([127, 0, 0, 1], 3001))
                    .await;
            });
            
            // En mode test, on simule une boucle infinie pour garder le bot "actif"
            info!("✅ Mode test activé - Interface disponible, backend simulé");
            loop {
                tokio::time::sleep(tokio::time::Duration::from_secs(30)).await;
                info!("💓 Bot test heartbeat - Interface disponible");
            }
        } else {
            info!("🔑 Token Discord chargé depuis DISCORD_TOKEN");
            env_token
        }
    } else if let Ok(env_token) = std::env::var("DISCORD_BOT_TOKEN") {
        info!("🔑 Token Discord chargé depuis DISCORD_BOT_TOKEN (Railway)");
        env_token
    } else {
        info!("🔑 Chargement du token depuis le stockage sécurisé local");
        let secure_storage = voidbot_shared::SecureStorage::new()
            .expect("Impossible d'initialiser le stockage sécurisé");
        
        match secure_storage.load_token().await {
            Ok(Some(secure_token)) => secure_token.as_str().to_string(),
            Ok(None) => {
                error!("❌ Aucun token Discord trouvé dans le stockage sécurisé");
                error!("💡 Utilisez l'interface pour vous connecter via le Web Login");
                return Ok(());
            }
            Err(e) => {
                error!("❌ Erreur lors du chargement du token sécurisé: {}", e);
                return Err(e);
            }
        }
    };

    // Intents pour selfbot (moins restrictifs que pour les bots)
    let intents = GatewayIntents::GUILD_MESSAGES
        | GatewayIntents::DIRECT_MESSAGES
        | GatewayIntents::MESSAGE_CONTENT
        | GatewayIntents::GUILDS
        | GatewayIntents::GUILD_MEMBERS
        | GatewayIntents::GUILD_PRESENCES
        | GatewayIntents::GUILD_VOICE_STATES;

    let mut client = Client::builder(&token, intents)
        .event_handler(Handler)
        .await?;

    // Get user ID for notification handler
    let current_user = client.http.get_current_user().await?;
    let user_id = current_user.id;

    // Note: Base de données gérée par l'app desktop via IPC

    // Initialize rate limiter
    let rate_limit_config = RateLimitConfig::default();
    let rate_limiter = Arc::new(RateLimiter::new(rate_limit_config));

    // Initialize event emitter (mode test ou production)
    let event_emitter = if test_mode {
        Arc::new(create_test_event_emitter())
    } else {
        // En mode production, on pourrait créer un canal IPC ici
        // Pour l'instant, on utilise le mode test
        Arc::new(create_test_event_emitter())
    };

    // Initialize bot data
    let bot_data = Arc::new(BotData {
        stealth_manager: Arc::new(Mutex::new(StealthManager::new())),
        config,
        notification_handler: Arc::new(DiscordNotificationHandler::new_with_emitter(user_id, event_emitter.clone())),
        troll_manager: Arc::new(TrollManager::new()),
        auto_commands_manager: Arc::new(AutoCommandsManager::new()),
        giveaway_joiner: Arc::new(GiveawayJoiner::new().expect("Failed to create GiveawayJoiner")),
        nitro_sniper: Arc::new(NitroSniper::new().expect("Failed to create NitroSniper")),
        rate_limiter,
        activity_tracker: Arc::new(ActivityTracker::new()),
        event_emitter,
    });

    {
        let mut data = client.data.write().await;
        data.insert::<BotData>(bot_data.clone());
    }

    // Démarrer le nettoyage des trolls expirés en arrière-plan
    let troll_manager_cleanup = bot_data.troll_manager.clone();
    tokio::spawn(async move {
        let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(60)); // Chaque minute
        loop {
            interval.tick().await;
            troll_manager_cleanup.cleanup_expired_trolls().await;
        }
    });

    // Démarrer le serveur HTTP pour Railway health checks
    let port = std::env::var("PORT")
        .unwrap_or_else(|_| "3000".to_string())
        .parse::<u16>()
        .unwrap_or(3000);
    
    let health = warp::path("health")
        .and(warp::get())
        .map(|| {
            warp::reply::json(&serde_json::json!({
                "status": "healthy",
                "service": "voidbot-discord",
                "timestamp": chrono::Utc::now().to_rfc3339()
            }))
        });
    
    let routes = health.with(warp::cors().allow_any_origin());
    
    // Lancer le serveur HTTP en arrière-plan
    tokio::spawn(async move {
        info!("🌐 Serveur HTTP health check démarré sur port {}", port);
        warp::serve(routes)
            .run(([0, 0, 0, 0], port))
            .await;
    });

    info!("🚀 Démarrage du selfbot Discord...");
    client.start().await?;

    Ok(())
}